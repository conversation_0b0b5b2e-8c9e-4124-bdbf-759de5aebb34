#!/usr/bin/env python3
"""
OpenAPI Specification Merger

This script merges all OpenAPI specification files in the api/spec directory
into a comprehensive bundled specification.
"""

import os
import sys
import yaml
import json
from pathlib import Path
from typing import Dict, Any, List

def load_yaml_file(file_path: str) -> Dict[str, Any]:
    """Load and parse a YAML file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        print(f"Warning: Could not load {file_path}: {e}")
        return {}

def merge_dicts(base: Dict[str, Any], overlay: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively merge two dictionaries."""
    result = base.copy()
    
    for key, value in overlay.items():
        if key in result:
            if isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = merge_dicts(result[key], value)
            elif isinstance(result[key], list) and isinstance(value, list):
                # Merge lists, avoiding duplicates
                result[key] = result[key] + [item for item in value if item not in result[key]]
            else:
                # Overlay value takes precedence
                result[key] = value
        else:
            result[key] = value
    
    return result

def enhance_info_section(base_info: Dict[str, Any], master_info: Dict[str, Any]) -> Dict[str, Any]:
    """Enhance the info section with detailed description and metadata."""
    enhanced = base_info.copy()
    
    # Use more detailed description from master if available
    if 'description' in master_info and len(str(master_info['description'])) > len(str(base_info.get('description', ''))):
        enhanced['description'] = master_info['description']
        print("  - Enhanced with detailed description from master.yaml")
    
    # Merge contact and license info
    if 'contact' in master_info:
        enhanced['contact'] = merge_dicts(enhanced.get('contact', {}), master_info['contact'])
        print("  - Enhanced contact information")
    
    if 'license' in master_info:
        enhanced['license'] = merge_dicts(enhanced.get('license', {}), master_info['license'])
        print("  - Enhanced license information")
    
    return enhanced

def enhance_security_schemes(base_components: Dict[str, Any], master_components: Dict[str, Any]) -> Dict[str, Any]:
    """Enhance security schemes with detailed configurations."""
    enhanced = base_components.copy()

    if 'securitySchemes' in master_components:
        base_security = enhanced.get('securitySchemes', {})
        master_security = master_components['securitySchemes']

        # Merge security schemes
        enhanced['securitySchemes'] = merge_dicts(base_security, master_security)
        print("  - Enhanced security schemes")

    return enhanced

def resolve_external_references(data: Any, merged_spec: Dict[str, Any]) -> Any:
    """Resolve external file references to internal references."""
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            if key == "$ref" and isinstance(value, str):
                # Convert external references to internal ones
                if "../master.yaml#/" in value:
                    result[key] = value.replace("../master.yaml#/", "#/")
                elif "../parking-api.yaml#/" in value:
                    result[key] = value.replace("../parking-api.yaml#/", "#/")
                elif "../components/schemas.yaml#/components/schemas/" in value:
                    # Extract schema name and convert to internal reference
                    schema_name = value.split("/")[-1]
                    result[key] = f"#/components/schemas/{schema_name}"
                elif "../components/responses.yaml#/components/responses/" in value:
                    # Extract response name and convert to internal reference
                    response_name = value.split("/")[-1]
                    result[key] = f"#/components/responses/{response_name}"
                elif "../components/" in value and "#/components/" in value:
                    # Handle any other component references
                    # Extract the component type and name from the reference
                    parts = value.split("#/components/")
                    if len(parts) == 2:
                        component_path = parts[1]  # e.g., "schemas/SomeSchema" or "responses/SomeResponse"
                        result[key] = f"#/components/{component_path}"
                    else:
                        result[key] = value
                elif "./schemas.yaml#/components/schemas/" in value:
                    # Handle relative references within component files
                    schema_name = value.split("/")[-1]
                    result[key] = f"#/components/schemas/{schema_name}"
                elif "./responses.yaml#/components/responses/" in value:
                    # Handle relative references within component files
                    response_name = value.split("/")[-1]
                    result[key] = f"#/components/responses/{response_name}"
                else:
                    result[key] = value
            else:
                result[key] = resolve_external_references(value, merged_spec)
        return result
    elif isinstance(data, list):
        return [resolve_external_references(item, merged_spec) for item in data]
    else:
        return data

def merge_openapi_specs() -> str:
    """Merge all OpenAPI specification files into a comprehensive bundle."""
    print("Creating comprehensive OpenAPI specification from all files...")
    
    spec_dir = Path("api/spec")
    
    # Load base specification (parking-api.yaml)
    base_file = spec_dir / "parking-api.yaml"
    if not base_file.exists():
        print(f"Error: Base specification file {base_file} not found!")
        sys.exit(1)
    
    print(f"  - Loading base specification: {base_file}")
    base_spec = load_yaml_file(str(base_file))
    
    # Load master specification for enhancements
    master_file = spec_dir / "master.yaml"
    master_spec = {}
    if master_file.exists():
        print(f"  - Loading master specification: {master_file}")
        master_spec = load_yaml_file(str(master_file))
    
    # Start with base specification
    merged_spec = base_spec.copy()
    
    # Enhance info section
    if 'info' in master_spec:
        merged_spec['info'] = enhance_info_section(
            merged_spec.get('info', {}), 
            master_spec['info']
        )
    
    # Enhance components (especially security schemes)
    if 'components' in master_spec:
        merged_spec['components'] = enhance_security_schemes(
            merged_spec.get('components', {}),
            master_spec['components']
        )
    
    # Load additional component files
    components_dir = spec_dir / "components"
    if components_dir.exists():
        for component_file in components_dir.glob("*.yaml"):
            print(f"  - Processing component file: {component_file.name}")
            component_data = load_yaml_file(str(component_file))
            
            # Merge component data into main components section
            if component_data:
                if 'components' not in merged_spec:
                    merged_spec['components'] = {}
                
                # Handle different component types
                if 'components' in component_data:
                    # Handle nested components structure
                    for comp_type, comp_items in component_data['components'].items():
                        if comp_type in ['schemas', 'responses', 'parameters', 'examples', 'requestBodies', 'headers', 'securitySchemes', 'links', 'callbacks']:
                            if comp_type not in merged_spec['components']:
                                merged_spec['components'][comp_type] = {}
                            merged_spec['components'][comp_type].update(comp_items)
                            print(f"    - Added {len(comp_items)} {comp_type}")
                else:
                    # Handle flat component structure (legacy support)
                    for key, value in component_data.items():
                        if key in ['schemas', 'responses', 'parameters', 'examples', 'requestBodies', 'headers', 'securitySchemes', 'links', 'callbacks']:
                            if key not in merged_spec['components']:
                                merged_spec['components'][key] = {}
                            merged_spec['components'][key].update(value)
                            print(f"    - Added {len(value)} {key}")
    
    # Load additional path files
    paths_dir = spec_dir / "paths"
    if paths_dir.exists():
        for path_file in paths_dir.glob("*.yaml"):
            print(f"  - Processing path file: {path_file.name}")
            path_data = load_yaml_file(str(path_file))

            # Merge path data into main paths section
            if path_data:
                if 'paths' not in merged_spec:
                    merged_spec['paths'] = {}

                # Resolve external references in path data
                resolved_paths = resolve_external_references(path_data, merged_spec)
                merged_spec['paths'].update(resolved_paths)
                print(f"    - Added {len(resolved_paths)} paths with resolved references")

    # Final pass: resolve any remaining external references in the entire spec
    print("  - Performing final reference resolution pass...")
    merged_spec = resolve_external_references(merged_spec, merged_spec)

    # Write the comprehensive bundled specification
    output_file = spec_dir / "bundled.yaml"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(merged_spec, f, default_flow_style=False, sort_keys=False, allow_unicode=True)
        
        print(f"  - Comprehensive specification written to: {output_file}")
        
        # Count total elements
        total_paths = len(merged_spec.get('paths', {}))
        total_schemas = len(merged_spec.get('components', {}).get('schemas', {}))
        total_responses = len(merged_spec.get('components', {}).get('responses', {}))
        
        print(f"  - Bundle contains: {total_paths} paths, {total_schemas} schemas, {total_responses} responses")
        
        return str(output_file)
        
    except Exception as e:
        print(f"Error writing bundled specification: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        output_file = merge_openapi_specs()
        print(f"SUCCESS: Created comprehensive bundle: {output_file}")
    except Exception as e:
        print(f"ERROR: Failed to create bundle: {e}")
        sys.exit(1)
