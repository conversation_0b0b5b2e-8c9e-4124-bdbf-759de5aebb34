components:
  responses:
    BadRequest:
      description: Bad request or validation error
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/components/schemas/ErrorResponse'

    Unauthorized:
      description: Unauthorized access
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/components/schemas/ErrorResponse'

    Forbidden:
      description: Forbidden access
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/components/schemas/ErrorResponse'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/components/schemas/ErrorResponse'

    Conflict:
      description: Resource conflict
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/components/schemas/ErrorResponse'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/components/schemas/ErrorResponse'